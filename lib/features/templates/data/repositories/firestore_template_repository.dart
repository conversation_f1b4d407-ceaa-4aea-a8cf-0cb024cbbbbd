import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/entities/template_item.dart';

class FirestoreTemplateRepository {
  final FirebaseFirestore _firestore;
  DocumentSnapshot? _lastDocument; // Track the last document for pagination
  bool _hasMoreTemplates = true; // Flag to indicate if more templates are available
  String? _currentCategory; // Track current category filter

  FirestoreTemplateRepository({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  /// Reset pagination state
  void resetPagination() {
    _lastDocument = null;
    _hasMoreTemplates = true;
    _currentCategory = null;
  }

  /// Reset pagination for category change
  void resetPaginationForCategory(String? category) {
    _lastDocument = null;
    _hasMoreTemplates = true;
    _currentCategory = category;
  }

  /// Check if more templates are available
  bool get hasMoreTemplates => _hasMoreTemplates;

  /// Get current category filter
  String? get currentCategory => _currentCategory;

  /// Fetches templates from Firestore with pagination and optional category filtering
  Future<List<TemplateItem>> getTemplates({
    int limit = 5,
    String? category,
  }) async {
    try {
      // If no more templates are available, return empty list
      if (!_hasMoreTemplates) {
        return [];
      }

      // If category changed, reset pagination
      if (category != _currentCategory) {
        resetPaginationForCategory(category);
      }

      // Build query
      Query query = _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true)
          .orderBy('uploadDate', descending: true);

      // Add category filter if specified
      if (category != null && category.isNotEmpty && category != 'All') {
        query = query.where('category', isEqualTo: category);
      }

      // Add pagination
      if (_lastDocument != null) {
        query = query.startAfterDocument(_lastDocument!);
      }

      // Apply limit
      query = query.limit(limit);

      AppLogger.info('Fetching templates - Category: ${category ?? "All"}, Limit: $limit');

      final querySnapshot = await query.get();

      // Update pagination state
      if (querySnapshot.docs.isEmpty || querySnapshot.docs.length < limit) {
        _hasMoreTemplates = false;
        AppLogger.info('No more templates available for category: ${category ?? "All"} - Got ${querySnapshot.docs.length} docs, limit was $limit');
      } else {
        _lastDocument = querySnapshot.docs.last;
        _hasMoreTemplates = true;
        AppLogger.info('Next page available for category: ${category ?? "All"} - Got ${querySnapshot.docs.length} docs');
      }

      // Convert documents to TemplateItem objects
      final templates = querySnapshot.docs
          .map((doc) => TemplateItem.fromFirestore(doc))
          .toList();

      AppLogger.info('Fetched ${templates.length} templates for category: ${category ?? "All"}');
      return templates;
    } catch (e) {
      AppLogger.error('Failed to fetch templates from Firestore', e);
      return [];
    }
  }

  /// Get all available categories from templates
  Future<List<String>> getAvailableCategories() async {
    try {
      final querySnapshot = await _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true)
          .get();

      // Extract unique categories
      final categories = <String>{};
      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        final category = data['category'] as String?;
        if (category != null && category.isNotEmpty) {
          categories.add(category);
        }
      }

      // Convert to list and sort
      final categoryList = categories.toList()..sort();

      AppLogger.info('Found ${categoryList.length} categories: $categoryList');
      return categoryList;
    } catch (e) {
      AppLogger.error('Failed to fetch categories from Firestore', e);
      return [];
    }
  }

  /// Get template count for each category
  Future<Map<String, int>> getCategoryCounts() async {
    try {
      final querySnapshot = await _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true)
          .get();

      // Count templates per category
      final categoryCounts = <String, int>{};
      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        final category = data['category'] as String?;
        if (category != null && category.isNotEmpty) {
          categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
        }
      }

      AppLogger.info('Category counts: $categoryCounts');
      return categoryCounts;
    } catch (e) {
      AppLogger.error('Failed to fetch category counts from Firestore', e);
      return {};
    }
  }

  /// Get total template count
  Future<int> getTotalTemplateCount() async {
    try {
      final querySnapshot = await _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true)
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      AppLogger.error('Failed to fetch total template count from Firestore', e);
      return 0;
    }
  }

  /// Get template count for specific category
  Future<int> getTemplateCountForCategory(String category) async {
    try {
      Query query = _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true);

      if (category != 'All') {
        query = query.where('category', isEqualTo: category);
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs.length;
    } catch (e) {
      AppLogger.error('Failed to fetch template count for category: $category', e);
      return 0;
    }
  }

  /// Increment view count for a template
  Future<void> incrementViewCount(String templateId) async {
    try {
      await _firestore.collection('templates').doc(templateId).update({
        'viewCount': FieldValue.increment(1),
      });
      AppLogger.info('Incremented view count for template: $templateId');
    } catch (e) {
      AppLogger.error('Failed to increment view count for template: $templateId', e);
    }
  }

  /// Increment download count for a template
  Future<void> incrementDownloadCount(String templateId) async {
    try {
      await _firestore.collection('templates').doc(templateId).update({
        'downloadCount': FieldValue.increment(1),
      });
      AppLogger.info('Incremented download count for template: $templateId');
    } catch (e) {
      AppLogger.error('Failed to increment download count for template: $templateId', e);
    }
  }

  /// Search templates by name or tags
  Future<List<TemplateItem>> searchTemplates({
    required String searchTerm,
    String? category,
    int limit = 20,
  }) async {
    try {
      // Build base query
      Query query = _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true);

      // Add category filter if specified
      if (category != null && category.isNotEmpty && category != 'All') {
        query = query.where('category', isEqualTo: category);
      }

      final querySnapshot = await query.get();

      // Filter by search term in memory (Firestore doesn't support full-text search)
      final searchTermLower = searchTerm.toLowerCase();
      final filteredTemplates = querySnapshot.docs
          .map((doc) => TemplateItem.fromFirestore(doc))
          .where((template) {
            final nameMatch = template.name.toLowerCase().contains(searchTermLower);
            final descriptionMatch = template.description?.toLowerCase().contains(searchTermLower) ?? false;
            final tagsMatch = template.tags?.any((tag) => tag.toLowerCase().contains(searchTermLower)) ?? false;
            return nameMatch || descriptionMatch || tagsMatch;
          })
          .take(limit)
          .toList();

      AppLogger.info('Found ${filteredTemplates.length} templates matching "$searchTerm"');
      return filteredTemplates;
    } catch (e) {
      AppLogger.error('Failed to search templates', e);
      return [];
    }
  }
}
