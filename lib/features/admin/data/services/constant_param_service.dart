import 'package:cloud_firestore/cloud_firestore.dart';

class ConstantParamService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'constantparam';

  // Cache for better performance
  Map<String, dynamic>? _cachedParams;
  DateTime? _lastFetchTime;
  static const Duration _cacheExpiry = Duration(minutes: 30);

  /// Get template categories and subcategories
  Future<Map<String, dynamic>> getTemplateCategories() async {
    try {
      // Check cache first
      if (_cachedParams != null && 
          _lastFetchTime != null && 
          DateTime.now().difference(_lastFetchTime!) < _cacheExpiry) {
        return _cachedParams!;
      }

      final doc = await _firestore
          .collection(_collection)
          .doc('template_categories')
          .get();

      if (doc.exists) {
        _cachedParams = doc.data() ?? {};
        _lastFetchTime = DateTime.now();
        return _cachedParams!;
      } else {
        // Create default document if it doesn't exist
        final defaultData = _getDefaultTemplateCategories();
        await _firestore
            .collection(_collection)
            .doc('template_categories')
            .set(defaultData);
        
        _cachedParams = defaultData;
        _lastFetchTime = DateTime.now();
        return defaultData;
      }
    } catch (e) {
      print('Error fetching template categories: $e');
      // Return default data on error
      return _getDefaultTemplateCategories();
    }
  }

  /// Update template categories
  Future<void> updateTemplateCategories(Map<String, dynamic> data) async {
    try {
      await _firestore
          .collection(_collection)
          .doc('template_categories')
          .set(data, SetOptions(merge: true));
      
      // Clear cache to force refresh
      _cachedParams = null;
      _lastFetchTime = null;
    } catch (e) {
      print('Error updating template categories: $e');
      throw Exception('Failed to update template categories: $e');
    }
  }

  /// Add new category
  Future<void> addCategory(String category, List<String> subCategories) async {
    try {
      final currentData = await getTemplateCategories();
      
      // Update categories list
      List<String> categories = List<String>.from(currentData['categories'] ?? []);
      if (!categories.contains(category)) {
        categories.add(category);
      }

      // Update subcategories map
      Map<String, dynamic> subCategoriesMap = Map<String, dynamic>.from(currentData['subCategories'] ?? {});
      subCategoriesMap[category] = subCategories;

      // Update other lists if they don't exist
      final updatedData = {
        'categories': categories,
        'subCategories': subCategoriesMap,
        'layoutTypes': currentData['layoutTypes'] ?? _getDefaultLayoutTypes(),
        'targetAudiences': currentData['targetAudiences'] ?? _getDefaultTargetAudiences(),
        'designStyles': currentData['designStyles'] ?? _getDefaultDesignStyles(),
        'usageTypes': currentData['usageTypes'] ?? _getDefaultUsageTypes(),
        'resolutions': currentData['resolutions'] ?? _getDefaultResolutions(),
        'licenseTypes': currentData['licenseTypes'] ?? _getDefaultLicenseTypes(),
        'lastUpdated': FieldValue.serverTimestamp(),
      };

      await updateTemplateCategories(updatedData);
    } catch (e) {
      print('Error adding category: $e');
      throw Exception('Failed to add category: $e');
    }
  }

  /// Remove category
  Future<void> removeCategory(String category) async {
    try {
      final currentData = await getTemplateCategories();
      
      // Update categories list
      List<String> categories = List<String>.from(currentData['categories'] ?? []);
      categories.remove(category);

      // Update subcategories map
      Map<String, dynamic> subCategoriesMap = Map<String, dynamic>.from(currentData['subCategories'] ?? {});
      subCategoriesMap.remove(category);

      final updatedData = {
        ...currentData,
        'categories': categories,
        'subCategories': subCategoriesMap,
        'lastUpdated': FieldValue.serverTimestamp(),
      };

      await updateTemplateCategories(updatedData);
    } catch (e) {
      print('Error removing category: $e');
      throw Exception('Failed to remove category: $e');
    }
  }

  /// Clear cache (useful for testing or manual refresh)
  void clearCache() {
    _cachedParams = null;
    _lastFetchTime = null;
  }

  /// Get default template categories structure
  Map<String, dynamic> _getDefaultTemplateCategories() {
    return {
      'categories': [
        'Business', 'Event', 'Festival', 'Party', 'Education', 'Health',
        'Technology', 'Food', 'Travel', 'Sports', 'Fashion', 'Real Estate'
      ],
      'subCategories': {
        'Business': ['Flyers', 'Brochures', 'Business Cards', 'Presentations', 'Logos'],
        'Event': ['Invitations', 'Tickets', 'Programs', 'Banners', 'Posters'],
        'Festival': ['Celebration', 'Religious', 'Cultural', 'Seasonal'],
        'Party': ['Birthday', 'Wedding', 'Anniversary', 'Graduation'],
        'Education': ['Certificates', 'Presentations', 'Worksheets', 'Posters'],
        'Health': ['Awareness', 'Medical', 'Fitness', 'Nutrition'],
        'Technology': ['App Promotion', 'Software', 'Digital Services'],
        'Food': ['Menu', 'Recipe', 'Restaurant', 'Catering'],
        'Travel': ['Tourism', 'Hotels', 'Transportation', 'Adventure'],
        'Sports': ['Team', 'Tournament', 'Fitness', 'Equipment'],
        'Fashion': ['Clothing', 'Accessories', 'Beauty', 'Style'],
        'Real Estate': ['Property', 'Rental', 'Commercial', 'Residential'],
      },
      'layoutTypes': _getDefaultLayoutTypes(),
      'targetAudiences': _getDefaultTargetAudiences(),
      'designStyles': _getDefaultDesignStyles(),
      'usageTypes': _getDefaultUsageTypes(),
      'resolutions': _getDefaultResolutions(),
      'licenseTypes': _getDefaultLicenseTypes(),
      'createdAt': FieldValue.serverTimestamp(),
      'lastUpdated': FieldValue.serverTimestamp(),
    };
  }

  List<String> _getDefaultLayoutTypes() => ['Horizontal', 'Vertical', 'Square'];
  
  List<String> _getDefaultTargetAudiences() => [
    'Small Business', 'Event Organizers', 'Educators', 'Marketers',
    'Students', 'Professionals', 'General Public'
  ];
  
  List<String> _getDefaultDesignStyles() => [
    'Modern', 'Minimalist', 'Retro', 'Elegant', 'Creative', 'Professional', 'Playful'
  ];
  
  List<String> _getDefaultUsageTypes() => ['Print', 'Social Media', 'Online Advertising', 'Email', 'Web'];
  
  List<String> _getDefaultResolutions() => ['High', 'Medium', 'Low'];
  
  List<String> _getDefaultLicenseTypes() => [
    'Free for personal use', 'Commercial use allowed', 'Premium license required'
  ];
}
