import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/fancy_card.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../data/services/constant_param_service.dart';

class ConstantParamManagementPage extends StatefulWidget {
  const ConstantParamManagementPage({super.key});

  @override
  State<ConstantParamManagementPage> createState() => _ConstantParamManagementPageState();
}

class _ConstantParamManagementPageState extends State<ConstantParamManagementPage> {
  final ConstantParamService _constantParamService = ConstantParamService();

  // Loading states
  bool _isLoading = true;
  bool _isSaving = false;

  // Data
  List<String> _categories = [];
  Map<String, List<String>> _subCategories = {};
  List<String> _layoutTypes = [];
  List<String> _targetAudiences = [];
  List<String> _designStyles = [];
  List<String> _usageTypes = [];
  List<String> _resolutions = [];
  List<String> _licenseTypes = [];

  // Controllers for adding new items
  final _newCategoryController = TextEditingController();
  final _newSubCategoryController = TextEditingController();
  String? _selectedCategoryForSubCategory;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _newCategoryController.dispose();
    _newSubCategoryController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      final params = await _constantParamService.getTemplateCategories();

      setState(() {
        _categories = List<String>.from(params['categories'] ?? []);

        final subCategoriesData = params['subCategories'] ?? {};
        _subCategories = {};
        subCategoriesData.forEach((key, value) {
          _subCategories[key] = List<String>.from(value ?? []);
        });

        _layoutTypes = List<String>.from(params['layoutTypes'] ?? []);
        _targetAudiences = List<String>.from(params['targetAudiences'] ?? []);
        _designStyles = List<String>.from(params['designStyles'] ?? []);
        _usageTypes = List<String>.from(params['usageTypes'] ?? []);
        _resolutions = List<String>.from(params['resolutions'] ?? []);
        _licenseTypes = List<String>.from(params['licenseTypes'] ?? []);

        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
      }
    }
  }

  Future<void> _addCategory() async {
    if (_newCategoryController.text.trim().isEmpty) return;

    try {
      setState(() => _isSaving = true);

      await _constantParamService.addCategory(
        _newCategoryController.text.trim(),
        [], // Empty subcategories initially
      );

      _newCategoryController.clear();
      await _loadData(); // Reload data

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Category added successfully!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error adding category: $e')),
        );
      }
    } finally {
      setState(() => _isSaving = false);
    }
  }

  Future<void> _removeCategory(String category) async {
    try {
      setState(() => _isSaving = true);

      await _constantParamService.removeCategory(category);
      await _loadData(); // Reload data

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Category removed successfully!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error removing category: $e')),
        );
      }
    } finally {
      setState(() => _isSaving = false);
    }
  }

  Future<void> _addSubCategory() async {
    if (_newSubCategoryController.text.trim().isEmpty || _selectedCategoryForSubCategory == null) return;

    try {
      setState(() => _isSaving = true);

      // Get current subcategories for the selected category
      List<String> currentSubCategories = List<String>.from(_subCategories[_selectedCategoryForSubCategory] ?? []);
      currentSubCategories.add(_newSubCategoryController.text.trim());

      await _constantParamService.addCategory(
        _selectedCategoryForSubCategory!,
        currentSubCategories,
      );

      _newSubCategoryController.clear();
      _selectedCategoryForSubCategory = null;
      await _loadData(); // Reload data

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Sub-category added successfully!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error adding sub-category: $e')),
        );
      }
    } finally {
      setState(() => _isSaving = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Manage Template Parameters',
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(gradient: AppTheme.premiumGoldBlackGradient)
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: _isLoading
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Loading parameters...',
                      style: AppTheme.bodyMedium.copyWith(
                        color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
                      ),
                    ),
                  ],
                ),
              )
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildCategoriesSection(isPremium),
                    const SizedBox(height: 24),
                    _buildSubCategoriesSection(isPremium),
                    const SizedBox(height: 24),
                    _buildOtherParametersSection(isPremium),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildCategoriesSection(bool isPremium) {
    return FancyCard(
      backgroundColor: isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Categories',
            style: AppTheme.headingMedium.copyWith(
              color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Add new category
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _newCategoryController,
                  decoration: InputDecoration(
                    labelText: 'New Category',
                    hintText: 'Enter category name',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    filled: true,
                    fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              SizedBox(
                width: 80,
                height: 48,
                child: GradientButton(
                  onPressed: _isSaving ? () {} : () => _addCategory(),
                  text: 'Add',
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Categories list
          if (_categories.isNotEmpty) ...[
            Text(
              'Current Categories:',
              style: AppTheme.bodyMedium.copyWith(
                color: isPremium ? AppTheme.premiumGold : AppTheme.textRichBlack,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _categories.map((category) => Chip(
                label: Text(category),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: _isSaving ? null : () => _removeCategory(category),
                backgroundColor: isPremium
                    ? AppTheme.premiumGold.withValues(alpha: 0.1)
                    : AppTheme.primaryBlue.withValues(alpha: 0.1),
              )).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSubCategoriesSection(bool isPremium) {
    return FancyCard(
      backgroundColor: isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sub-Categories',
            style: AppTheme.headingMedium.copyWith(
              color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Add new subcategory
          Column(
            children: [
              DropdownButtonFormField<String>(
                value: _selectedCategoryForSubCategory,
                decoration: InputDecoration(
                  labelText: 'Select Category',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                ),
                items: _categories.map((category) => DropdownMenuItem(
                  value: category,
                  child: Text(category),
                )).toList(),
                onChanged: (value) => setState(() => _selectedCategoryForSubCategory = value),
                isExpanded: true,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _newSubCategoryController,
                      decoration: InputDecoration(
                        labelText: 'New Sub-Category',
                        hintText: 'Enter sub-category name',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        filled: true,
                        fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  SizedBox(
                    width: 80,
                    height: 48,
                    child: GradientButton(
                      onPressed: _isSaving ? () {} : () => _addSubCategory(),
                      text: 'Add',
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Sub-categories list
          if (_subCategories.isNotEmpty) ...[
            Text(
              'Current Sub-Categories:',
              style: AppTheme.bodyMedium.copyWith(
                color: isPremium ? AppTheme.premiumGold : AppTheme.textRichBlack,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            for (final entry in _subCategories.entries) Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    entry.key,
                    style: AppTheme.bodyMedium.copyWith(
                      color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Wrap(
                    spacing: 6,
                    runSpacing: 6,
                    children: entry.value.map((subCategory) => Chip(
                      label: Text(subCategory, style: const TextStyle(fontSize: 12)),
                      backgroundColor: isPremium
                          ? AppTheme.premiumGold.withValues(alpha: 0.05)
                          : AppTheme.primaryBlue.withValues(alpha: 0.05),
                    )).toList(),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOtherParametersSection(bool isPremium) {
    return FancyCard(
      backgroundColor: isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Other Parameters',
            style: AppTheme.headingMedium.copyWith(
              color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          _buildParameterList('Layout Types', _layoutTypes, isPremium),
          const SizedBox(height: 12),
          _buildParameterList('Target Audiences', _targetAudiences, isPremium),
          const SizedBox(height: 12),
          _buildParameterList('Design Styles', _designStyles, isPremium),
          const SizedBox(height: 12),
          _buildParameterList('Usage Types', _usageTypes, isPremium),
          const SizedBox(height: 12),
          _buildParameterList('Resolutions', _resolutions, isPremium),
          const SizedBox(height: 12),
          _buildParameterList('License Types', _licenseTypes, isPremium),
        ],
      ),
    );
  }

  Widget _buildParameterList(String title, List<String> items, bool isPremium) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTheme.bodyMedium.copyWith(
            color: isPremium ? AppTheme.premiumGold : AppTheme.textRichBlack,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        Wrap(
          spacing: 6,
          runSpacing: 6,
          children: items.map((item) => Chip(
            label: Text(item, style: const TextStyle(fontSize: 12)),
            backgroundColor: isPremium
                ? AppTheme.premiumGold.withValues(alpha: 0.05)
                : AppTheme.primaryBlue.withValues(alpha: 0.05),
          )).toList(),
        ),
      ],
    );
  }
}
