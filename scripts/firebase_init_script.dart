import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';

/// Firebase initialization script to set up constant parameters
/// Run this script to populate the Firebase database with default template parameters
class FirebaseInitScript {
  static Future<void> main() async {
    print('🔥 Firebase Constant Parameters Initialization Script');
    print('=' * 60);
    
    try {
      // Initialize Firebase
      print('📱 Initializing Firebase...');
      await Firebase.initializeApp();
      print('✅ Firebase initialized successfully');
      
      // Get Firestore instance
      final firestore = FirebaseFirestore.instance;
      
      // Create the constant parameters document
      await _createConstantParamsDocument(firestore);
      
      print('\n🎉 Firebase setup completed successfully!');
      print('📊 Collection: constantparam');
      print('📄 Document: template_categories');
      print('✨ All template parameters have been initialized');
      
    } catch (e) {
      print('❌ Error during Firebase setup: $e');
      exit(1);
    }
  }
  
  static Future<void> _createConstantParamsDocument(FirebaseFirestore firestore) async {
    print('\n📊 Creating constant parameters document...');
    
    final constantParamsData = {
      'categories': [
        'Business',
        'Event', 
        'Festival',
        'Party',
        'Education',
        'Health',
        'Technology',
        'Food',
        'Travel',
        'Sports',
        'Fashion',
        'Real Estate'
      ],
      'subCategories': {
        'Business': [
          'Flyers',
          'Brochures', 
          'Business Cards',
          'Presentations',
          'Logos'
        ],
        'Event': [
          'Invitations',
          'Tickets',
          'Programs', 
          'Banners',
          'Posters'
        ],
        'Festival': [
          'Celebration',
          'Religious',
          'Cultural',
          'Seasonal'
        ],
        'Party': [
          'Birthday',
          'Wedding',
          'Anniversary',
          'Graduation'
        ],
        'Education': [
          'Certificates',
          'Presentations',
          'Worksheets',
          'Posters'
        ],
        'Health': [
          'Awareness',
          'Medical',
          'Fitness',
          'Nutrition'
        ],
        'Technology': [
          'App Promotion',
          'Software',
          'Digital Services'
        ],
        'Food': [
          'Menu',
          'Recipe',
          'Restaurant',
          'Catering'
        ],
        'Travel': [
          'Tourism',
          'Hotels',
          'Transportation',
          'Adventure'
        ],
        'Sports': [
          'Team',
          'Tournament',
          'Fitness',
          'Equipment'
        ],
        'Fashion': [
          'Clothing',
          'Accessories',
          'Beauty',
          'Style'
        ],
        'Real Estate': [
          'Property',
          'Rental',
          'Commercial',
          'Residential'
        ]
      },
      'layoutTypes': [
        'Horizontal',
        'Vertical',
        'Square'
      ],
      'targetAudiences': [
        'Small Business',
        'Event Organizers',
        'Educators',
        'Marketers',
        'Students',
        'Professionals',
        'General Public'
      ],
      'designStyles': [
        'Modern',
        'Minimalist',
        'Retro',
        'Elegant',
        'Creative',
        'Professional',
        'Playful'
      ],
      'usageTypes': [
        'Print',
        'Social Media',
        'Online Advertising',
        'Email',
        'Web'
      ],
      'resolutions': [
        'High',
        'Medium',
        'Low'
      ],
      'licenseTypes': [
        'Free for personal use',
        'Commercial use allowed',
        'Premium license required'
      ],
      'createdAt': FieldValue.serverTimestamp(),
      'lastUpdated': FieldValue.serverTimestamp(),
      'version': '1.0.0',
      'description': 'Template constant parameters for QuickPosters app',
    };
    
    // Check if document already exists
    final docRef = firestore.collection('constantparam').doc('template_categories');
    final docSnapshot = await docRef.get();
    
    if (docSnapshot.exists) {
      print('⚠️  Document already exists. Updating with new data...');
      await docRef.update({
        ...constantParamsData,
        'lastUpdated': FieldValue.serverTimestamp(),
        'updatedBy': 'firebase_init_script',
      });
      print('✅ Document updated successfully');
    } else {
      print('📝 Creating new document...');
      await docRef.set(constantParamsData);
      print('✅ Document created successfully');
    }
    
    // Verify the document was created/updated
    final verifySnapshot = await docRef.get();
    if (verifySnapshot.exists) {
      final data = verifySnapshot.data() as Map<String, dynamic>;
      print('\n📋 Document verification:');
      print('   Categories: ${(data['categories'] as List).length} items');
      print('   Sub-categories: ${(data['subCategories'] as Map).keys.length} categories');
      print('   Layout types: ${(data['layoutTypes'] as List).length} items');
      print('   Target audiences: ${(data['targetAudiences'] as List).length} items');
      print('   Design styles: ${(data['designStyles'] as List).length} items');
      print('   Usage types: ${(data['usageTypes'] as List).length} items');
      print('   Resolutions: ${(data['resolutions'] as List).length} items');
      print('   License types: ${(data['licenseTypes'] as List).length} items');
    } else {
      throw Exception('Failed to verify document creation');
    }
  }
}

/// Entry point for the script
void main() async {
  await FirebaseInitScript.main();
}
